import React, { memo, useCallback, useEffect, useMemo } from "react";
import { Typography } from "@mui/material";
import AppDatePicker from "@/components/common/AppDatePicker";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import {
  selectReturnDeviceHeaderDate,
  setHeaderDate,
  updateEditedValue,
  selectEditedRows,
} from "../../../returnDevice.slice";
import dayjs from "dayjs";
import { IReturnDeviceList } from "../../../returnDevice.model";

interface ReturnDatePickerHeaderProps {
  selectedRows: IReturnDeviceList[];
  approveTableSelectedRows?: IReturnDeviceList[];
}

const ReturnDatePickerHeader = ({
  selectedRows,
  approveTableSelectedRows,
}: ReturnDatePickerHeaderProps) => {
  const dispatch = useAppDispatch();
  const headerDate = useAppSelector(selectReturnDeviceHeaderDate);
  const editedData = useAppSelector(selectEditedRows);

  // Memoize the selected rows IDs to prevent unnecessary re-renders
  const selectedRowIds = useMemo(
    () => selectedRows?.map((row) => row.id) || [],
    [selectedRows]
  );

  // Memoize the approve table selected rows IDs
  const approveRowIds = useMemo(
    () => approveTableSelectedRows?.map((row) => row.id) || [],
    [approveTableSelectedRows]
  );

  // Update header date based on selected rows
  useEffect(() => {
    if (!selectedRows || selectedRows.length === 0) {
      dispatch(setHeaderDate(dayjs()));
      return;
    }

    const allDates = selectedRows.map((row) => {
      const edited = editedData[row.id];
      return edited?.borrowReturnDate ?? row.borrowReturnDate;
    });

    const firstDate = allDates[0];
    const allSame = allDates.every((date) => date === firstDate);

    if (!allSame) {
      dispatch(setHeaderDate(null));
    } else if (firstDate) {
      dispatch(setHeaderDate(dayjs(firstDate)));
    }
  }, [selectedRowIds, editedData, dispatch, selectedRows]);

  const handleHeaderDateChange = useCallback(
    (newValue: any) => {
      const newDate = newValue ? dayjs(newValue) : null;
      dispatch(setHeaderDate(newDate));

      // Update all approved selected rows with the new date
      approveTableSelectedRows?.forEach((row) => {
        dispatch(
          updateEditedValue({
            rowId: row.id,
            field: "borrowReturnDate",
            value: newValue || null,
          })
        );
      });
    },
    [dispatch, approveRowIds, approveTableSelectedRows]
  );

  return (
    <>
      <Typography variant="body1" color="primary.contrastText">
        Ngày trả
      </Typography>
      <AppDatePicker
        value={headerDate}
        onChange={handleHeaderDateChange}
        slotProps={{
          textField: {
            size: "small",
            fullWidth: true,
            placeholder: "Ngày trả cho tất cả",
          },
        }}
        maxDate={dayjs()}
      />
    </>
  );
};

ReturnDatePickerHeader.displayName = "ReturnDatePickerHeader";

// Custom comparison function to prevent unnecessary re-renders
const arePropsEqual = (
  prevProps: ReturnDatePickerHeaderProps,
  nextProps: ReturnDatePickerHeaderProps
) => {
  // Compare selectedRows by IDs
  const prevSelectedIds = prevProps.selectedRows?.map((row) => row.id) || [];
  const nextSelectedIds = nextProps.selectedRows?.map((row) => row.id) || [];

  if (prevSelectedIds.length !== nextSelectedIds.length) {
    return false;
  }

  const selectedRowsEqual = prevSelectedIds.every(
    (id, index) => id === nextSelectedIds[index]
  );

  // Compare approveTableSelectedRows by IDs
  const prevApproveIds =
    prevProps.approveTableSelectedRows?.map((row) => row.id) || [];
  const nextApproveIds =
    nextProps.approveTableSelectedRows?.map((row) => row.id) || [];

  if (prevApproveIds.length !== nextApproveIds.length) {
    return false;
  }

  const approveRowsEqual = prevApproveIds.every(
    (id, index) => id === nextApproveIds[index]
  );

  return selectedRowsEqual && approveRowsEqual;
};

export default memo(ReturnDatePickerHeader, arePropsEqual);
