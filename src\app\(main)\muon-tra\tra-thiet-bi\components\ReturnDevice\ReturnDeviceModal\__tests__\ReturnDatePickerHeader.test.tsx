import React from 'react';
import { render, screen } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import ReturnDatePickerHeader from '../ReturnDatePickerHeader';
import { returnDeviceSlice } from '../../../../returnDevice.slice';
import { IReturnDeviceList } from '../../../../returnDevice.model';

// Mock dayjs
jest.mock('dayjs', () => {
  const originalDayjs = jest.requireActual('dayjs');
  return {
    ...originalDayjs,
    default: jest.fn(() => ({
      format: jest.fn(() => '2024-01-01'),
      toDate: jest.fn(() => new Date('2024-01-01')),
    })),
  };
});

// Mock AppDatePicker
jest.mock('@/components/common/AppDatePicker', () => {
  return function MockAppDatePicker({ value, onChange, ...props }: any) {
    return (
      <input
        data-testid="date-picker"
        value={value?.format?.() || ''}
        onChange={(e) => onChange?.(e.target.value)}
        {...props}
      />
    );
  };
});

const mockStore = configureStore({
  reducer: {
    returnDevice: returnDeviceSlice.reducer,
  },
});

const mockSelectedRows: IReturnDeviceList[] = [
  {
    id: 1,
    borrowReturnDate: '2024-01-01',
    teacherName: 'Test Teacher',
    deviceName: 'Test Device',
  } as IReturnDeviceList,
];

const mockApproveRows: IReturnDeviceList[] = [
  {
    id: 1,
    borrowReturnDate: '2024-01-01',
    teacherName: 'Test Teacher',
    deviceName: 'Test Device',
  } as IReturnDeviceList,
];

describe('ReturnDatePickerHeader', () => {
  it('renders without crashing', () => {
    render(
      <Provider store={mockStore}>
        <ReturnDatePickerHeader
          selectedRows={mockSelectedRows}
          approveTableSelectedRows={mockApproveRows}
        />
      </Provider>
    );

    expect(screen.getByText('Ngày trả')).toBeInTheDocument();
    expect(screen.getByTestId('date-picker')).toBeInTheDocument();
  });

  it('renders with empty selected rows', () => {
    render(
      <Provider store={mockStore}>
        <ReturnDatePickerHeader
          selectedRows={[]}
          approveTableSelectedRows={[]}
        />
      </Provider>
    );

    expect(screen.getByText('Ngày trả')).toBeInTheDocument();
    expect(screen.getByTestId('date-picker')).toBeInTheDocument();
  });
});
