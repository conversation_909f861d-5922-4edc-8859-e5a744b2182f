import { createSlice, PayloadAction, WithSlice } from "@reduxjs/toolkit";
import { createSelector } from "@reduxjs/toolkit";
import {
  EditedDataRecord,
  IEditedDeviceData,
  ReturnDeviceFieldName,
} from "./returnDevice.model";
import { RootState } from "@/redux/store";
import dayjs, { Dayjs } from "dayjs";

interface ReturnDeviceState {
  editedData: EditedDataRecord;
  headerDate: Dayjs | null;
}

const initialState: ReturnDeviceState = {
  editedData: {},
  headerDate: dayjs(),
};

export const returnDeviceSlice = createSlice({
  name: "returnDevice",
  initialState,
  reducers: {
    updateEditedValue: (
      state,
      action: PayloadAction<{
        rowId: number;
        field: ReturnDeviceFieldName;
        value: number | string;
      }>
    ) => {
      const { rowId, field, value } = action.payload;
      if (!state.editedData[rowId]) {
        state.editedData[rowId] = {};
      }
      (state.editedData[rowId] as any)[field] = value;
    },
    resetEditedData: (state) => {
      state.editedData = {};
    },
    setEditedData: (state, action: PayloadAction<EditedDataRecord>) => {
      state.editedData = action.payload;
    },
    setHeaderDate: (state, action: PayloadAction<Dayjs | null>) => {
      state.headerDate = action.payload;
    },
    updateHeaderDateFromSelectedRows: (
      state,
      action: PayloadAction<{
        selectedRows: { id: number; borrowReturnDate: string }[];
      }>
    ) => {
      const { selectedRows } = action.payload;
      if (!selectedRows || selectedRows.length === 0) {
        state.headerDate = dayjs();
        return;
      }

      const allDates = selectedRows.map((row) => {
        const edited = state.editedData[row.id];
        return edited?.borrowReturnDate ?? row.borrowReturnDate;
      });

      const firstDate = allDates[0];
      const allSame = allDates.every((date) => date === firstDate);

      if (!allSame) {
        state.headerDate = null;
      } else if (firstDate) {
        state.headerDate = dayjs(firstDate);
      }
    },
  },
  selectors: {
    selectReturnDeviceState: (state) => state,
    selectEditedData: (state) => state.editedData,
    selectHeaderDate: (state) => state.headerDate,
  },
});

export const {
  updateEditedValue,
  resetEditedData,
  setEditedData,
  setHeaderDate,
  updateHeaderDateFromSelectedRows,
} = returnDeviceSlice.actions;

export const { selectReturnDeviceState, selectEditedData, selectHeaderDate } =
  returnDeviceSlice.selectors;

// Extend LazyLoadedSlices để có thể inject động
declare module "@/redux/reducer" {
  export interface LazyLoadedSlices
    extends WithSlice<typeof returnDeviceSlice> {}
}

// Inject reducer
import { rootReducer } from "@/redux/reducer";
const injectedReturnDeviceSlice = returnDeviceSlice.injectInto(rootReducer);
export const returnDeviceSelectors = injectedReturnDeviceSlice.selectors;

// Stable empty objects để tránh tạo reference mới
const EMPTY_EDITED_DATA: EditedDataRecord = {};
const EMPTY_DEVICE_DATA: IEditedDeviceData = {};

// Memoized selectors với createSelector cho từng row - sử dụng injected selectors
export const makeSelectEditedDataById = () =>
  createSelector(
    [returnDeviceSelectors.selectEditedData, (_: any, rowId: number) => rowId],
    (editedData, rowId) => editedData[rowId] ?? EMPTY_DEVICE_DATA
  );

export const makeSelectTotalBrokenById = () =>
  createSelector(
    [returnDeviceSelectors.selectEditedData, (_: any, rowId: number) => rowId],
    (editedData, rowId) => editedData[rowId]?.totalBroken
  );

export const makeSelectTotalLostById = () =>
  createSelector(
    [returnDeviceSelectors.selectEditedData, (_: any, rowId: number) => rowId],
    (editedData, rowId) => editedData[rowId]?.totalLost
  );

export const makeSelectTotalConsumedById = () =>
  createSelector(
    [returnDeviceSelectors.selectEditedData, (_: any, rowId: number) => rowId],
    (editedData, rowId) => editedData[rowId]?.totalConsumed
  );

export const makeSelectNotesById = () =>
  createSelector(
    [returnDeviceSelectors.selectEditedData, (_: any, rowId: number) => rowId],
    (editedData, rowId) => editedData[rowId]?.notes
  );

export const makeSelectBorrowReturnDateById = () =>
  createSelector(
    [returnDeviceSelectors.selectEditedData, (_: any, rowId: number) => rowId],
    (editedData, rowId) => editedData[rowId]?.borrowReturnDate
  );

// Selector để lấy tất cả edited rows cho submit
export const selectEditedRows = returnDeviceSelectors.selectEditedData;

// Memoized selector cho header date
export const selectReturnDeviceHeaderDate =
  returnDeviceSelectors.selectHeaderDate;

export default returnDeviceSlice.reducer;
